# -*- coding: utf-8 -*-
"""
激光模板查询器打包脚本
使用PyInstaller将应用程序打包为可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理目录: {dir_name}")

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('laser_templates.json', '.'),
        ('modern_style.py', '.'),
        ('modern_widgets.py', '.'),
        ('data_manager.py', '.'),
        ('settings_window.py', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'json',
        'datetime',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='激光模板查询器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file='version_info.txt'
)
'''
    
    with open('app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ 已创建PyInstaller规格文件")

def create_version_info():
    """创建版本信息文件"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2,0,0,0),
    prodvers=(2,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'激光模板查询器'),
         StringStruct(u'FileDescription', u'激光模板查询器 - 现代化版本'),
         StringStruct(u'FileVersion', u'*******'),
         StringStruct(u'InternalName', u'LaserTemplateQuery'),
         StringStruct(u'LegalCopyright', u'Copyright © 2024'),
         StringStruct(u'OriginalFilename', u'激光模板查询器.exe'),
         StringStruct(u'ProductName', u'激光模板查询器'),
         StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    print("✓ 已创建版本信息文件")

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("⚠️ PyInstaller未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            print("✓ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def build_executable():
    """构建可执行文件"""
    try:
        # 使用规格文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'app.spec']
        
        print("🔨 开始构建可执行文件...")
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 构建成功！")
            return True
        else:
            print("❌ 构建失败:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到dist目录"""
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 要复制的文件
    files_to_copy = [
        'laser_templates.json',
        '界面优化总结.md',
        'README.md' if os.path.exists('README.md') else None
    ]
    
    for file_name in files_to_copy:
        if file_name and os.path.exists(file_name):
            try:
                shutil.copy2(file_name, dist_dir)
                print(f"✓ 已复制文件: {file_name}")
            except Exception as e:
                print(f"⚠️ 复制文件 {file_name} 失败: {e}")
    
    return True

def create_readme():
    """创建使用说明文件"""
    readme_content = '''# 激光模板查询器 v2.0 - 现代化版本

## 🎉 新版本特性

### 界面优化
- ✨ 现代化扁平设计风格
- 🎨 统一的蓝色主题配色
- 📱 响应式布局，支持窗口大小调整
- 🔧 现代化UI组件库

### 用户体验提升
- 🚀 **重要改进**: 成功提示改为状态栏显示，无需点击弹窗
- ⚡ 流畅的操作体验
- 💡 清晰的状态反馈
- 🎯 优化的操作流程

## 📋 使用说明

### 启动程序
双击 `激光模板查询器.exe` 启动程序

### 主要功能
1. **单位选择**: 从下拉框选择或点击快速选择按钮
2. **激光要求**: 选择单位后，在列表中选择具体的激光要求
3. **模板代码**: 自动显示对应的激光模板代码
4. **复制功能**: 点击复制按钮将模板代码复制到剪贴板
5. **数据管理**: 通过设置窗口管理单位、要求和模板代码

### 数据管理
- 点击菜单栏的"设置"打开数据管理窗口
- 可以添加、编辑、删除单位和激光要求
- 可以编辑和保存模板代码
- 支持数据的导入和导出

### 新版本优势
- **无弹窗干扰**: 所有成功操作都在状态栏显示，3秒后自动恢复
- **现代化界面**: 专业的视觉设计，提升使用体验
- **响应式设计**: 适配不同屏幕尺寸和分辨率

## 📁 文件说明

- `激光模板查询器.exe`: 主程序文件
- `laser_templates.json`: 数据文件（包含所有单位、要求和模板）
- `界面优化总结.md`: 详细的优化说明文档

## 🔧 系统要求

- Windows 7 或更高版本
- 无需安装Python环境
- 建议屏幕分辨率: 1024x768 或更高

## 📞 技术支持

如有问题或建议，请联系开发团队。

---
激光模板查询器 v2.0 - 现代化版本
Copyright © 2024
'''
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ 已创建使用说明文件")

def main():
    """主函数"""
    print("🚀 激光模板查询器打包工具 v2.0")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ['main.py', 'data_manager.py', 'settings_window.py', 
                     'modern_style.py', 'modern_widgets.py']
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✓ 所有必要文件检查通过")
    
    # 清理构建目录
    clean_build_dirs()
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return False
    
    # 创建配置文件
    create_spec_file()
    create_version_info()
    create_readme()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print(f"📁 可执行文件位置: {os.path.abspath('dist')}")
    print("📋 包含文件:")
    
    dist_path = Path('dist')
    if dist_path.exists():
        for item in dist_path.iterdir():
            print(f"   - {item.name}")
    
    print("\n✨ 新版本特性:")
    print("   - 现代化界面设计")
    print("   - 状态栏提示替代弹窗")
    print("   - 响应式布局")
    print("   - 更好的用户体验")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n按任意键退出...")
        input()
    else:
        print("\n❌ 打包失败，请检查错误信息")
        input()