# -*- coding: utf-8 -*-
"""
创建发布包
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_release_package():
    """创建发布包"""
    # 创建发布目录
    release_name = f"激光模板查询器_v2.0_现代化版本_{datetime.now().strftime('%Y%m%d')}"
    release_dir = f"release/{release_name}"
    
    if os.path.exists("release"):
        try:
            shutil.rmtree("release")
        except PermissionError:
            print("⚠️ 无法删除旧的release目录，将创建新的版本")
            import time
            release_name = f"激光模板查询器_v2.1_滚轮修复版_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    os.makedirs(release_dir, exist_ok=True)
    
    # 复制dist目录下的所有文件
    dist_files = [
        "激光模板查询器.exe",
        "laser_templates.json", 
        "README.md",
        "界面优化总结.md",
        "使用说明.txt"
    ]
    
    for file_name in dist_files:
        src_path = f"dist/{file_name}"
        if os.path.exists(src_path):
            shutil.copy2(src_path, release_dir)
            print(f"✓ 已复制: {file_name}")
    
    # 创建ZIP压缩包
    zip_name = f"{release_name}.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, "release")
                zipf.write(file_path, arcname)
                print(f"✓ 已压缩: {file}")
    
    print(f"\n🎉 发布包创建完成！")
    print(f"📁 目录: {release_dir}")
    print(f"📦 压缩包: {zip_name}")
    print(f"📊 文件大小: {os.path.getsize(zip_name) / 1024 / 1024:.1f} MB")
    
    return zip_name

if __name__ == "__main__":
    create_release_package()