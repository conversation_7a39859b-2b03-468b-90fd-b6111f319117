# -*- coding: utf-8 -*-
"""
创建发布包 v2.0 - 滚轮修复版
"""

import os
import shutil
import zipfile
from datetime import datetime
import time

def create_release_package():
    """创建发布包"""
    # 创建带时间戳的发布目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    release_name = f"激光模板查询器_v2.1_滚轮修复版_{timestamp}"
    release_dir = f"release_new/{release_name}"
    
    # 确保目录不存在
    if os.path.exists("release_new"):
        try:
            shutil.rmtree("release_new")
        except:
            pass
    
    os.makedirs(release_dir, exist_ok=True)
    
    # 复制dist目录下的所有文件
    dist_files = [
        "激光模板查询器.exe",
        "laser_templates.json", 
        "README.md",
        "界面优化总结.md",
        "使用说明.txt"
    ]
    
    copied_files = []
    for file_name in dist_files:
        src_path = f"dist/{file_name}"
        if os.path.exists(src_path):
            try:
                # 使用不同的复制方法
                with open(src_path, 'rb') as src:
                    with open(f"{release_dir}/{file_name}", 'wb') as dst:
                        dst.write(src.read())
                copied_files.append(file_name)
                print(f"✓ 已复制: {file_name}")
            except Exception as e:
                print(f"⚠️ 复制失败 {file_name}: {e}")
    
    # 创建更新说明
    update_notes = f"""# 激光模板查询器 v2.1 - 滚轮修复版

## 🔧 本次更新 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})

### 修复内容
- ✅ **重要修复**: 解决滚动单位下拉框或激光要求列表框时整个页面跟着滚动的问题
- ✅ 现在滚轮事件只影响当前操作的组件，不会传播到父窗口
- ✅ 支持Windows和Linux系统的滚轮事件处理

### 技术改进
- 🔧 为ModernListbox添加了滚轮事件拦截
- 🔧 为ModernCombobox添加了滚轮事件处理
- 🔧 使用"break"返回值阻止事件传播

### 使用体验
- 📋 在下拉框中滚动：只改变选中项，页面不滚动
- 📝 在列表框中滚动：只滚动列表内容，页面不滚动
- 🖱️ 在空白区域滚动：页面正常滚动

## 📋 完整功能列表

### 界面优化 (v2.0)
- 🎨 现代化扁平设计风格
- 📱 响应式布局，支持窗口大小调整
- 🔵 统一的蓝色主题配色
- 💫 现代化UI组件和交互效果

### 用户体验提升 (v2.0)
- 🚀 成功提示改为状态栏显示，无需点击弹窗
- ⚡ 流畅的操作体验，特别是批量添加数据时
- 💡 清晰的状态反馈，3秒后自动恢复
- 🎯 优化的操作流程

### 滚轮修复 (v2.1)
- 🔧 修复滚轮事件传播问题
- 🖱️ 精确的滚轮控制
- 🎯 更好的用户操作体验

## 🎯 使用说明

1. **启动程序**: 双击"激光模板查询器.exe"
2. **选择单位**: 使用下拉框或快速选择按钮
3. **选择要求**: 在列表中选择激光要求
4. **查看模板**: 自动显示对应的模板代码
5. **复制使用**: 点击复制按钮复制到剪贴板

## 📞 技术支持

如有问题或建议，请联系开发团队。

---
激光模板查询器 v2.1 - 滚轮修复版
更新时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
"""
    
    # 保存更新说明
    with open(f"{release_dir}/更新说明.md", 'w', encoding='utf-8') as f:
        f.write(update_notes)
    copied_files.append("更新说明.md")
    print("✓ 已创建: 更新说明.md")
    
    # 创建ZIP压缩包
    zip_name = f"{release_name}.zip"
    try:
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(release_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, "release_new")
                    zipf.write(file_path, arcname)
                    print(f"✓ 已压缩: {file}")
        
        file_size = os.path.getsize(zip_name) / 1024 / 1024
        
        print(f"\n🎉 滚轮修复版发布包创建完成！")
        print(f"📁 目录: {release_dir}")
        print(f"📦 压缩包: {zip_name}")
        print(f"📊 文件大小: {file_size:.1f} MB")
        print(f"📋 包含文件: {len(copied_files)} 个")
        
        print(f"\n🔧 本次修复:")
        print("  - 解决滚轮事件传播问题")
        print("  - 下拉框和列表框滚轮操作更精确")
        print("  - 提升用户操作体验")
        
        return zip_name
        
    except Exception as e:
        print(f"❌ 创建压缩包失败: {e}")
        return None

if __name__ == "__main__":
    print("🔧 激光模板查询器 v2.1 - 滚轮修复版打包工具")
    print("=" * 60)
    create_release_package()