# -*- coding: utf-8 -*-
"""
数据管理模块
负责激光模板数据的存储、读取和管理
"""

import json
import os
import sys
from typing import Dict, List, Any
import shutil
from datetime import datetime


class DataManager:
    def __init__(self, data_file: str = "laser_templates.json"):
        """
        初始化数据管理器

        Args:
            data_file: 数据文件路径
        """
        # 获取合适的数据文件路径
        self.data_file = self._get_data_file_path(data_file)
        self.data = {
            "units": {},  # 单位数据：{单位名: {requirements: [要求列表], templates: {要求: 模板代码}}}
            "version": "1.0",
            "last_modified": ""
        }
        self.load_data()

    def _get_data_file_path(self, filename: str) -> str:
        """
        获取数据文件的完整路径
        无论在EXE环境还是开发环境，都保存到程序所在目录
        """
        if getattr(sys, 'frozen', False):
            # 运行在PyInstaller打包的exe中
            # 获取EXE文件所在的目录
            exe_dir = os.path.dirname(sys.executable)
            data_file_path = os.path.join(exe_dir, filename)
            print(f"EXE模式：数据将保存到 {data_file_path}")
            return data_file_path
        else:
            # 运行在正常Python环境中，使用当前目录
            print(f"开发模式：数据将保存到 {filename}")
            return filename
    
    def load_data(self):
        """从文件加载数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                    # 确保数据结构完整
                    if "units" in loaded_data:
                        self.data = loaded_data
                    else:
                        # 兼容旧版本数据格式
                        self.data["units"] = loaded_data
                print(f"数据加载成功：{len(self.data['units'])} 个单位")
            else:
                # 如果数据文件不存在，尝试加载示例数据
                print("数据文件不存在，正在初始化...")
                self.load_sample_data()
                # 立即保存到用户目录
                self.save_data()
        except Exception as e:
            print(f"数据加载失败: {e}")
            self.load_sample_data()
            # 尝试保存默认数据
            try:
                self.save_data()
            except Exception as save_error:
                print(f"保存默认数据失败: {save_error}")
    
    def load_sample_data(self):
        """加载示例数据"""
        # 尝试多个可能的示例数据位置
        sample_files = ["sample_data.json"]

        # 在EXE环境中，也尝试从临时目录加载
        if getattr(sys, 'frozen', False):
            # PyInstaller会将数据文件解压到临时目录
            temp_sample = os.path.join(sys._MEIPASS, "sample_data.json")
            sample_files.insert(0, temp_sample)

        for sample_file in sample_files:
            if os.path.exists(sample_file):
                try:
                    with open(sample_file, 'r', encoding='utf-8') as f:
                        sample_data = json.load(f)
                        self.data["units"] = sample_data.get("units", {})
                    print(f"示例数据加载成功：{len(self.data['units'])} 个单位 (来源: {sample_file})")
                    return
                except Exception as e:
                    print(f"从 {sample_file} 加载示例数据失败: {e}")
                    continue

        # 如果所有示例数据文件都不存在，创建默认数据
        print("未找到示例数据文件，创建默认数据")
        self._create_default_data()
    
    def _create_default_data(self):
        """创建默认数据"""
        self.data["units"] = {
            "示例单位A": {
                "requirements": ["激光切割要求1", "激光焊接要求1"],
                "templates": {
                    "激光切割要求1": "G0 X0 Y0\nM3 S1000\nG1 X10 Y10 F100\nM5",
                    "激光焊接要求1": "G0 X0 Y0\nM3 S800\nG1 X5 Y5 F50\nM5"
                }
            }
        }
        print("已创建默认数据")
    
    def save_data(self):
        """保存数据到文件"""
        try:
            # 备份现有文件
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                shutil.copy2(self.data_file, backup_file)
            
            # 更新修改时间
            self.data["last_modified"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 保存数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            print("数据保存成功")
            return True
        except Exception as e:
            print(f"数据保存失败: {e}")
            return False
    
    def get_units(self) -> List[str]:
        """获取所有单位名称列表"""
        return list(self.data["units"].keys())
    
    def get_requirements(self, unit: str) -> List[str]:
        """获取指定单位的激光要求列表"""
        if unit in self.data["units"]:
            return self.data["units"][unit].get("requirements", [])
        return []
    
    def get_template(self, unit: str, requirement: str) -> str:
        """获取指定单位和要求的模板代码"""
        if unit in self.data["units"]:
            templates = self.data["units"][unit].get("templates", {})
            return templates.get(requirement, "")
        return ""
    
    def add_unit(self, unit_name: str) -> bool:
        """添加新单位"""
        if unit_name and unit_name not in self.data["units"]:
            self.data["units"][unit_name] = {
                "requirements": [],
                "templates": {}
            }
            return True
        return False
    
    def delete_unit(self, unit_name: str) -> bool:
        """删除单位"""
        if unit_name in self.data["units"]:
            del self.data["units"][unit_name]
            return True
        return False
    
    def rename_unit(self, old_name: str, new_name: str) -> bool:
        """重命名单位"""
        if old_name in self.data["units"] and new_name and new_name not in self.data["units"]:
            self.data["units"][new_name] = self.data["units"].pop(old_name)
            return True
        return False
    
    def add_requirement(self, unit: str, requirement: str, template: str = "") -> bool:
        """为指定单位添加激光要求"""
        if unit in self.data["units"] and requirement:
            if requirement not in self.data["units"][unit]["requirements"]:
                self.data["units"][unit]["requirements"].append(requirement)
                self.data["units"][unit]["templates"][requirement] = template
                return True
        return False
    
    def delete_requirement(self, unit: str, requirement: str) -> bool:
        """删除指定单位的激光要求"""
        if unit in self.data["units"] and requirement in self.data["units"][unit]["requirements"]:
            self.data["units"][unit]["requirements"].remove(requirement)
            if requirement in self.data["units"][unit]["templates"]:
                del self.data["units"][unit]["templates"][requirement]
            return True
        return False
    
    def update_template(self, unit: str, requirement: str, template: str) -> bool:
        """更新模板代码"""
        if unit in self.data["units"] and requirement in self.data["units"][unit]["requirements"]:
            self.data["units"][unit]["templates"][requirement] = template
            return True
        return False
    
    def export_data(self, export_file: str) -> bool:
        """导出数据到指定文件"""
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"数据导出失败: {e}")
            return False
    
    def import_data(self, import_file: str) -> bool:
        """从指定文件导入数据"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_data = json.load(f)
                if "units" in imported_data:
                    self.data = imported_data
                else:
                    # 兼容旧格式
                    self.data["units"] = imported_data
                return True
        except Exception as e:
            print(f"数据导入失败: {e}")
            return False
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        total_units = len(self.data["units"])
        total_requirements = sum(len(unit_data["requirements"]) for unit_data in self.data["units"].values())
        total_templates = sum(len(unit_data["templates"]) for unit_data in self.data["units"].values())
        
        return {
            "total_units": total_units,
            "total_requirements": total_requirements,
            "total_templates": total_templates,
            "last_modified": self.data.get("last_modified", "未知"),
            "version": self.data.get("version", "1.0")
        }
