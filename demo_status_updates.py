# -*- coding: utf-8 -*-
"""
演示状态栏更新效果
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modern_style import ModernStyle
from modern_widgets import ModernButton, ModernCard, setup_ttk_styles

def create_demo():
    """创建演示窗口"""
    # 获取样式配置
    style_config = ModernStyle.get_style()
    colors = style_config['colors']
    fonts = style_config['fonts']
    spacing = style_config['spacing']
    
    # 创建主窗口
    root = tk.Tk()
    root.title("状态栏更新演示")
    root.geometry("500x400")
    root.configure(bg=colors['background'])
    
    # 设置样式
    setup_ttk_styles()
    
    # 创建主卡片
    main_card = ModernCard(root, title="数据操作演示")
    main_card.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 状态栏
    status_container = tk.Frame(
        root, 
        bg=colors['surface'], 
        height=36,
        highlightbackground=colors['border'],
        highlightthickness=1
    )
    status_container.pack(side=tk.BOTTOM, fill=tk.X)
    status_container.pack_propagate(False)

    status_inner = tk.Frame(status_container, bg=colors['surface'])
    status_inner.pack(fill=tk.BOTH, expand=True, padx=spacing['md'], pady=spacing['sm'])

    status_label = tk.Label(
        status_inner,
        text="就绪",
        font=fonts['body'],
        fg=colors['text_secondary'],
        bg=colors['surface']
    )
    status_label.pack(side=tk.LEFT)
    
    def update_status(message):
        """更新状态栏"""
        status_label.config(text=message, fg=colors['success'])
        # 3秒后恢复默认状态
        root.after(3000, lambda: status_label.config(text="就绪", fg=colors['text_secondary']))
    
    # 演示按钮
    def demo_add_unit():
        update_status("✓ 单位 '新单位' 添加成功")
    
    def demo_add_requirement():
        update_status("✓ 激光要求 '新要求' 添加成功")
    
    def demo_save_template():
        update_status("✓ 模板代码保存成功: 单位A - 要求B")
    
    def demo_import_data():
        update_status("✓ 数据导入成功: data.json")
    
    # 创建演示按钮
    tk.Label(
        main_card.content_frame,
        text="点击按钮查看状态栏更新效果\n(不再弹出烦人的成功对话框)",
        font=fonts['body'],
        bg=colors['surface'],
        fg=colors['text'],
        justify=tk.CENTER
    ).pack(pady=20)
    
    btn_frame = tk.Frame(main_card.content_frame, bg=colors['surface'])
    btn_frame.pack(pady=10)
    
    ModernButton(btn_frame, text="添加单位", command=demo_add_unit, 
                style_type="default").pack(side=tk.LEFT, padx=5)
    ModernButton(btn_frame, text="添加要求", command=demo_add_requirement, 
                style_type="default").pack(side=tk.LEFT, padx=5)
    
    btn_frame2 = tk.Frame(main_card.content_frame, bg=colors['surface'])
    btn_frame2.pack(pady=5)
    
    ModernButton(btn_frame2, text="保存模板", command=demo_save_template, 
                style_type="success").pack(side=tk.LEFT, padx=5)
    ModernButton(btn_frame2, text="导入数据", command=demo_import_data, 
                style_type="primary").pack(side=tk.LEFT, padx=5)
    
    # 说明文字
    tk.Label(
        main_card.content_frame,
        text="✨ 优化完成！\n• 成功提示显示在状态栏\n• 无需点击弹窗确认\n• 3秒后自动恢复就绪状态",
        font=fonts['small'],
        bg=colors['surface'],
        fg=colors['text_muted'],
        justify=tk.CENTER
    ).pack(pady=20)
    
    return root

if __name__ == "__main__":
    demo_root = create_demo()
    demo_root.mainloop()