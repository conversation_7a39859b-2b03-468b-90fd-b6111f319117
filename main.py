# -*- coding: utf-8 -*-
"""
激光模板查询器主程序
提供三级查询功能：单位 -> 激光要求 -> 模板代码
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager
from settings_window import SettingsWindow
from modern_style import ModernStyle
from modern_widgets import ModernButton, ModernListbox, ModernCard, ModernCombobox, ModernScrollableFrame, setup_ttk_styles
from scroll_fix import apply_scroll_fix


class LaserTemplateQueryApp:
    def __init__(self):
        """初始化激光模板查询器应用"""
        self.root = tk.Tk()
        self.root.title("🔥 激光模板查询器 v2.0")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)

        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self.on_window_resize)
        
        # 初始化数据管理器
        self.data_manager = DataManager()

        # 窗口置顶状态
        self.always_on_top = tk.BooleanVar(value=False)

        # 当前选中的数据
        self.current_unit = None
        self.current_requirement = None

        # 获取现代化样式配置
        self.style_config = ModernStyle.get_style()
        self.colors = self.style_config['colors']
        self.icons = self.style_config['icons']
        self.fonts = self.style_config['fonts']
        self.spacing = self.style_config['spacing']

        # 配置现代化样式
        self.setup_modern_style()

        # 创建界面
        # 创建界面
        self.create_widgets()
        self.load_units()

        # 应用滚轮事件修复
        self.scroll_fixer = apply_scroll_fix(self.root)
        
        # 保护下拉框和列表框不受滚轮事件传播影响
        self.root.after(200, self.apply_scroll_protection)

        # 初始化响应式布局
        self.root.after(100, self.initial_responsive_setup)

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_modern_style(self):
        """配置现代化界面样式"""
        # 设置根窗口背景色
        self.root.configure(bg=self.colors['background'])

        # 配置ttk样式
        self.style = setup_ttk_styles()

        # 初始化响应式变量
        self.current_scale = 1.0
        self.base_font_size = 10
        self.last_width = 800
        self.last_height = 600

    def add_button_hover_effect(self, button):
        """为按钮添加悬停效果"""
        def on_enter(event):
            button.configure(cursor='hand2')

        def on_leave(event):
            button.configure(cursor='')

        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)

    def on_window_resize(self, event):
        """窗口大小变化时的响应方法"""
        # 只响应主窗口的大小变化
        if event.widget != self.root:
            return

        current_width = self.root.winfo_width()
        current_height = self.root.winfo_height()

        # 避免频繁更新
        if abs(current_width - self.last_width) < 10 and abs(current_height - self.last_height) < 10:
            return

        self.last_width = current_width
        self.last_height = current_height

        # 计算缩放比例（基于宽度，最小0.6，最大1.2）
        base_width = 750
        scale = max(0.6, min(1.2, current_width / base_width))

        if abs(scale - self.current_scale) > 0.05:  # 只有显著变化时才更新
            self.current_scale = scale
            self.update_responsive_layout()

    def update_responsive_layout(self):
        """更新响应式布局"""
        try:
            # 计算新的字体大小
            base_font_size = max(8, int(self.base_font_size * self.current_scale))

            # 更新按钮样式
            padding_h = max(self.spacing['sm'], int(self.spacing['md'] * self.current_scale))
            padding_v = max(self.spacing['xs'], int(self.spacing['sm'] * self.current_scale))

            self.style.configure('Modern.TButton',
                               padding=(padding_h, padding_v),
                               font=('微软雅黑', base_font_size))

            self.style.configure('Primary.TButton',
                               padding=(max(self.spacing['md'], int(self.spacing['lg'] * self.current_scale)), 
                                       max(self.spacing['sm'], int(self.spacing['md'] * self.current_scale))),
                               font=('微软雅黑', base_font_size, 'bold'))

            self.style.configure('Success.TButton',
                               padding=(padding_h, padding_v),
                               font=('微软雅黑', base_font_size, 'bold'))

            # 更新组合框样式
            combo_padding = max(self.spacing['sm'], int(self.spacing['md'] * self.current_scale))
            self.style.configure('Modern.TCombobox',
                               padding=combo_padding,
                               font=('微软雅黑', base_font_size))

            # 更新列表框字体
            if hasattr(self, 'req_listbox') and hasattr(self.req_listbox, 'listbox'):
                list_font_size = max(10, int(11 * self.current_scale))
                self.req_listbox.listbox.configure(font=("微软雅黑", list_font_size))

            # 更新模板显示字体
            if hasattr(self, 'template_entry'):
                template_font_size = max(10, int(11 * self.current_scale))
                self.template_entry.configure(font=("Consolas", template_font_size))

            if hasattr(self, 'template_text'):
                template_font_size = max(10, int(11 * self.current_scale))
                self.template_text.configure(font=("Consolas", template_font_size))

        except Exception:
            # 静默处理错误，避免影响用户体验
            pass

    def apply_scroll_protection(self):
        """应用滚轮事件保护"""
        try:
            # 保护单位下拉框
            if hasattr(self, 'unit_combobox'):
                self.scroll_fixer.protect_widget(self.unit_combobox)
            
            # 保护激光要求列表框
            if hasattr(self, 'req_listbox') and hasattr(self.req_listbox, 'listbox'):
                self.scroll_fixer.protect_widget(self.req_listbox.listbox)
                
            print("✓ 滚轮事件保护已应用")
        except Exception as e:
            print(f"⚠️ 滚轮保护应用失败: {e}")

    def initial_responsive_setup(self):
        """初始化响应式设置"""
        # 获取当前窗口大小并设置初始缩放
        current_width = self.root.winfo_width()
        if current_width > 0:  # 确保窗口已经显示
            base_width = 750
            self.current_scale = max(0.6, min(1.2, current_width / base_width))
            self.update_responsive_layout()
    
    def create_widgets(self):
        """创建主界面组件"""
        # 创建菜单栏
        self.create_menu()

        # 主框架
        main_frame = tk.Frame(
            self.root, 
            bg=self.colors['background'], 
            relief='flat', 
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=self.spacing['md'], pady=self.spacing['md'])

        # 顶部控制面板 - 使用现代化卡片
        control_card = ModernCard(main_frame, title="控制面板")
        control_card.pack(fill=tk.X, pady=(0, self.spacing['md']))

        # 左侧控制组
        left_controls = tk.Frame(control_card.content_frame, bg=self.colors['surface'])
        left_controls.pack(side=tk.LEFT)

        # 窗口置顶复选框
        self.topmost_check = ttk.Checkbutton(
            left_controls,
            text=f"{self.icons['pin']} 窗口置顶",
            variable=self.always_on_top,
            command=self.toggle_topmost,
            style='Modern.TCheckbutton'
        )
        self.topmost_check.pack(side=tk.LEFT)

        # 刷新按钮
        refresh_btn = ModernButton(
            left_controls,
            text="刷新",
            icon=self.icons['refresh'],
            command=self.refresh_data,
            style_type="default"
        )
        refresh_btn.pack(side=tk.LEFT, padx=(self.spacing['sm'], 0))

        # 右侧控制组
        right_controls = tk.Frame(control_card.content_frame, bg=self.colors['surface'])
        right_controls.pack(side=tk.RIGHT)

        # 设置按钮
        settings_btn = ModernButton(
            right_controls,
            text="数据设置",
            icon=self.icons['settings'],
            command=self.open_settings,
            style_type="primary"
        )
        settings_btn.pack(side=tk.RIGHT)
        
        # 创建可滚动的三级查询面板
        scrollable_container = ModernScrollableFrame(main_frame)
        scrollable_container.pack(fill=tk.BOTH, expand=True)
        
        # 使用可滚动框架作为查询面板的父容器
        query_frame = scrollable_container.scrollable_frame

        # 第一级：单位选择 - 使用现代化卡片
        unit_card = ModernCard(query_frame, title=f"{self.icons['unit']} 选择单位")
        unit_card.pack(fill=tk.X, pady=(0, self.spacing['sm']))

        # 单位选择方式框架
        unit_select_frame = tk.Frame(unit_card.content_frame, bg=self.colors['surface'])
        unit_select_frame.pack(fill=tk.X, pady=self.spacing['sm'])

        # 下拉列表方式
        dropdown_frame = tk.Frame(unit_select_frame, bg=self.colors['surface'])
        dropdown_frame.pack(side=tk.LEFT)

        tk.Label(
            dropdown_frame,
            text="下拉选择:",
            font=self.fonts['small'],
            background=self.colors['surface'],
            foreground=self.colors['text_secondary']
        ).pack(side=tk.LEFT)

        # 创建支持模糊搜索的组合框
        self.unit_var = tk.StringVar()
        self.unit_combobox = ModernCombobox(
            dropdown_frame,
            textvariable=self.unit_var,
            state="normal",
            width=18
        )
        self.unit_combobox.pack(side=tk.LEFT, padx=(self.spacing['sm'], 0))

        # 绑定事件
        self.unit_combobox.bind('<<ComboboxSelected>>', self.on_unit_selected)
        self.unit_combobox.bind('<KeyRelease>', self.on_unit_search)
        self.unit_combobox.bind('<FocusIn>', self.on_unit_focus_in)
        self.unit_combobox.bind('<FocusOut>', self.on_unit_focus_out)

        # 存储所有单位列表用于搜索
        self.all_units = []

        # 添加搜索提示
        search_hint = tk.Label(
            dropdown_frame,
            text=self.icons['search'],
            font=self.fonts['small'],
            foreground=self.colors['text_muted'],
            background=self.colors['surface']
        )
        search_hint.pack(side=tk.LEFT, padx=(self.spacing['xs'], 0))

        # 分隔线
        separator = tk.Frame(unit_select_frame, width=2, bg=self.colors['border'])
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=self.spacing['lg'])

        # 按钮选择方式
        buttons_section = tk.Frame(unit_select_frame, bg=self.colors['surface'])
        buttons_section.pack(side=tk.LEFT, fill=tk.X, expand=True)

        tk.Label(
            buttons_section,
            text="快速选择:",
            font=self.fonts['small'],
            background=self.colors['surface'],
            foreground=self.colors['text_secondary']
        ).pack(side=tk.LEFT)

        self.unit_buttons_frame = tk.Frame(buttons_section, bg=self.colors['surface'])
        self.unit_buttons_frame.pack(side=tk.LEFT, padx=(self.spacing['md'], 0), fill=tk.X, expand=True)
        
        # 第二级：激光要求选择 - 使用现代化卡片
        req_card = ModernCard(query_frame, title=f"{self.icons['requirement']} 选择激光要求")
        req_card.pack(fill=tk.X, pady=(0, self.spacing['sm']))

        # 要求列表框架
        req_list_container = tk.Frame(req_card.content_frame, bg=self.colors['surface'])
        req_list_container.pack(fill=tk.X, pady=self.spacing['sm'])

        # 使用现代化列表框
        self.req_listbox = ModernListbox(req_list_container, height=4)
        self.req_listbox.pack(fill=tk.BOTH, expand=True)

        # 绑定事件
        self.req_listbox.bind('<<ListboxSelect>>', self.on_requirement_selected)
        self.req_listbox.bind('<Double-Button-1>', self.on_requirement_double_click)

        # 添加提示文本
        hint_label = tk.Label(
            req_list_container,
            text="💡 提示：双击激光要求可直接复制模板代码",
            font=self.fonts['tiny'],
            foreground=self.colors['text_muted'],
            background=self.colors['surface']
        )
        hint_label.pack(pady=(self.spacing['xs'], 0))
        
        # 第三级：模板代码显示 - 使用现代化卡片
        template_card = ModernCard(query_frame, title=f"{self.icons['code']} 激光模板代码")
        template_card.pack(fill=tk.BOTH, expand=True)

        # 模板信息显示
        info_frame = tk.Frame(template_card.content_frame, bg=self.colors['surface'])
        info_frame.pack(fill=tk.X, pady=self.spacing['sm'])

        # 状态指示器
        status_frame = tk.Frame(info_frame, bg=self.colors['surface'])
        status_frame.pack(side=tk.LEFT)

        self.status_indicator = tk.Label(
            status_frame,
            text=self.icons['ready'],
            font=self.fonts['body'],
            background=self.colors['surface'],
            foreground=self.colors['primary']
        )
        self.status_indicator.pack(side=tk.LEFT)

        tk.Label(
            status_frame,
            text="当前模板:",
            font=self.fonts['small'],
            background=self.colors['surface'],
            foreground=self.colors['text_secondary']
        ).pack(side=tk.LEFT, padx=(self.spacing['xs'], 0))

        self.template_info_label = tk.Label(
            info_frame,
            text="请选择单位和激光要求",
            foreground=self.colors['text_secondary'],
            font=self.fonts['subtitle'],
            background=self.colors['surface']
        )
        self.template_info_label.pack(side=tk.LEFT, padx=(self.spacing['sm'], 0))

        # 模板代码显示区域
        text_container = tk.Frame(template_card.content_frame, bg=self.colors['surface'])
        text_container.pack(fill=tk.BOTH, expand=True, pady=self.spacing['sm'])

        # 代码显示框架
        code_frame = tk.Frame(
            text_container,
            bg=self.colors['card'],
            relief='flat',
            bd=0,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        code_frame.pack(fill=tk.BOTH, expand=True)

        # 代码内容框架
        code_inner = tk.Frame(code_frame, bg=self.colors['card'])
        code_inner.pack(fill=tk.BOTH, expand=True, padx=self.spacing['md'], pady=self.spacing['md'])

        # 单行代码显示（Entry）
        self.template_var = tk.StringVar()
        self.template_entry = tk.Entry(
            code_inner,
            textvariable=self.template_var,
            font=self.fonts['code'],
            bg=self.colors['card'],
            fg=self.colors['text'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            state='readonly',
            readonlybackground=self.colors['card']
        )
        self.template_entry.pack(fill=tk.X, pady=self.spacing['xs'])

        # 多行代码显示（Text）
        self.template_text = tk.Text(
            code_inner,
            height=3,
            wrap=tk.WORD,
            font=self.fonts['code'],
            bg=self.colors['card'],
            fg=self.colors['text'],
            selectbackground=self.colors['primary'],
            selectforeground=self.colors['text_inverse'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            state='disabled'
        )
        # 默认隐藏多行文本框
        
        # 模板操作按钮
        btn_container = tk.Frame(template_card.content_frame, bg=self.colors['surface'])
        btn_container.pack(fill=tk.X, pady=self.spacing['sm'])

        # 按钮框架
        template_btn_frame = tk.Frame(
            btn_container, 
            bg=self.colors['background'], 
            relief='flat', 
            bd=0,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        template_btn_frame.pack(fill=tk.X)

        # 内部按钮容器
        btn_inner = tk.Frame(template_btn_frame, bg=self.colors['background'])
        btn_inner.pack(fill=tk.X, padx=self.spacing['md'], pady=self.spacing['sm'])

        # 左侧按钮组
        left_btn_group = tk.Frame(btn_inner, bg=self.colors['background'])
        left_btn_group.pack(side=tk.LEFT)

        # 复制按钮
        copy_btn = ModernButton(
            left_btn_group,
            text="复制",
            icon=self.icons['copy'],
            command=self.copy_template,
            style_type="success"
        )
        copy_btn.pack(side=tk.LEFT, padx=(0, self.spacing['xs']))

        # 清空按钮
        clear_btn = ModernButton(
            left_btn_group,
            text="清空",
            icon=self.icons['clear'],
            command=self.clear_template_display,
            style_type="default"
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, self.spacing['xs']))

        # 右侧按钮组
        right_btn_group = tk.Frame(btn_inner, bg=self.colors['background'])
        right_btn_group.pack(side=tk.RIGHT)

        # 保存按钮
        save_btn = ModernButton(
            right_btn_group,
            text="保存到文件",
            icon=self.icons['save'],
            command=self.save_template_to_file,
            style_type="primary"
        )
        save_btn.pack(side=tk.RIGHT)

        # 现代扁平化状态栏
        status_container = tk.Frame(
            self.root, 
            bg=self.colors['surface'], 
            height=36,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        status_container.pack(side=tk.BOTTOM, fill=tk.X)
        status_container.pack_propagate(False)

        # 状态栏内容
        status_inner = tk.Frame(status_container, bg=self.colors['surface'])
        status_inner.pack(fill=tk.BOTH, expand=True, padx=self.spacing['md'], pady=self.spacing['sm'])

        # 左侧状态信息
        status_left = tk.Frame(status_inner, bg=self.colors['surface'])
        status_left.pack(side=tk.LEFT)

        self.status_bar = tk.Label(
            status_left,
            text=f"{self.icons['ready']} 就绪",
            font=self.fonts['body'],
            fg=self.colors['text_secondary'],
            bg=self.colors['surface']
        )
        self.status_bar.pack(side=tk.LEFT)

        # 右侧信息显示
        status_right = tk.Frame(status_inner, bg=self.colors['surface'])
        status_right.pack(side=tk.RIGHT)

        # 版本信息
        version_label = tk.Label(
            status_right,
            text=f"{self.icons['sparkle']} 激光模板查询器 v2.0",
            font=self.fonts['body'],
            fg=self.colors['text_muted'],
            bg=self.colors['surface']
        )
        version_label.pack(side=tk.RIGHT)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="刷新数据", command=self.refresh_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="数据管理", command=self.open_settings)
        settings_menu.add_checkbutton(label="窗口置顶", variable=self.always_on_top, command=self.toggle_topmost)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def load_units(self):
        """加载单位数据"""
        units = self.data_manager.get_units()

        # 保存所有单位用于搜索
        self.all_units = units.copy()

        # 更新下拉列表
        self.unit_combobox['values'] = units
        if units:
            self.unit_var.set('')  # 使用StringVar清空

        # 更新快速选择按钮
        for widget in self.unit_buttons_frame.winfo_children():
            widget.destroy()

        # 根据窗口大小动态调整按钮布局
        buttons_per_row = max(3, min(6, int(self.current_scale * 5)))
        
        for i, unit in enumerate(units):
            btn = ModernButton(
                self.unit_buttons_frame,
                text=unit,
                command=lambda u=unit: self.select_unit(u),
                style_type="default"
            )
            btn.pack(side=tk.LEFT, padx=self.spacing['xs'], pady=self.spacing['xs'])

            # 动态换行
            if i > 0 and (i + 1) % buttons_per_row == 0:
                tk.Frame(self.unit_buttons_frame, height=self.spacing['xs'], bg=self.colors['surface']).pack()

        # 清空其他显示
        self.req_listbox.delete(0, tk.END)
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_status(f"📊 已加载 {len(units)} 个单位")
    
    def on_unit_selected(self, event):
        """下拉列表单位选择事件"""
        unit = self.unit_combobox.get()
        if unit and unit in self.all_units:
            self.select_unit(unit)

    def on_unit_search(self, event):
        """单位搜索事件"""
        search_text = self.unit_var.get().lower()

        if not search_text:
            # 如果搜索框为空，显示所有单位
            filtered_units = self.all_units
        else:
            # 模糊搜索匹配
            filtered_units = []
            for unit in self.all_units:
                # 支持拼音首字母、中文、部分匹配
                if (search_text in unit.lower() or
                    self.match_pinyin(unit, search_text) or
                    self.match_partial(unit, search_text)):
                    filtered_units.append(unit)

        # 更新下拉列表
        self.unit_combobox['values'] = filtered_units

        # 如果只有一个匹配项且完全匹配，自动选择
        if len(filtered_units) == 1 and search_text == filtered_units[0].lower():
            self.select_unit(filtered_units[0])

        # 显示下拉列表
        if filtered_units and len(search_text) > 0:
            self.unit_combobox.event_generate('<Down>')

    def on_unit_focus_in(self, event):
        """单位输入框获得焦点"""
        # 显示所有可选项
        self.unit_combobox['values'] = self.all_units
        self.update_status("💡 提示：输入单位名称进行搜索，支持拼音首字母")

    def on_unit_focus_out(self, event):
        """单位输入框失去焦点"""
        current_text = self.unit_var.get()
        if current_text and current_text not in self.all_units:
            # 如果输入的不是有效单位，尝试找到最佳匹配
            best_match = self.find_best_match(current_text)
            if best_match:
                self.unit_var.set(best_match)
                self.select_unit(best_match)
            else:
                # 清空无效输入
                self.unit_var.set("")
                self.update_status("⚠️ 未找到匹配的单位")

    def match_pinyin(self, unit, search_text):
        """匹配拼音首字母"""
        # 扩展的拼音首字母映射
        pinyin_map = {
            # 航空制造部
            '航': 'h', '空': 'k', '制': 'z', '造': 'z', '部': 'b',
            # 汽车制造部
            '汽': 'q', '车': 'c', '发': 'f', '动': 'd', '机': 'j',
            # 电子制造部
            '电': 'd', '子': 'z', '元': 'y', '件': 'j',
            # 医疗器械部
            '医': 'y', '疗': 'l', '器': 'q', '械': 'x',
            # 其他常用字
            '生': 's', '产': 'c', '工': 'g', '厂': 'c', '公': 'g', '司': 's',
            '技': 'j', '术': 's', '研': 'y', '究': 'j', '开': 'k', '发': 'f',
            '设': 's', '计': 'j', '装': 'z', '配': 'p', '维': 'w', '修': 'x',
            '质': 'z', '量': 'l', '检': 'j', '测': 'c', '控': 'k',
            '材': 'c', '料': 'l', '金': 'j', '属': 's', '塑': 's', '胶': 'j',
            '精': 'j', '密': 'm', '仪': 'y', '表': 'b', '光': 'g', '学': 'x'
        }

        # 生成拼音首字母
        unit_pinyin = ''.join(pinyin_map.get(char, char.lower()) for char in unit)

        # 检查是否匹配
        return (search_text in unit_pinyin or
                unit_pinyin.startswith(search_text) or
                any(search_text in unit_pinyin[i:] for i in range(len(unit_pinyin))))

    def match_partial(self, unit, search_text):
        """部分匹配"""
        # 检查是否包含搜索文本的任何部分
        for i in range(len(search_text)):
            for j in range(i + 1, len(search_text) + 1):
                if search_text[i:j] in unit:
                    return True
        return False

    def find_best_match(self, search_text):
        """找到最佳匹配的单位"""
        search_lower = search_text.lower()

        # 优先级匹配
        for unit in self.all_units:
            # 完全匹配
            if search_lower == unit.lower():
                return unit

        for unit in self.all_units:
            # 开头匹配
            if unit.lower().startswith(search_lower):
                return unit

        for unit in self.all_units:
            # 包含匹配
            if search_lower in unit.lower():
                return unit

        for unit in self.all_units:
            # 拼音匹配
            if self.match_pinyin(unit, search_lower):
                return unit

        return None
    
    def select_unit(self, unit):
        """选择单位"""
        self.current_unit = unit
        self.current_requirement = None

        # 更新下拉列表显示
        self.unit_var.set(unit)

        # 加载激光要求
        self.load_requirements()

        # 清空模板显示
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_template_info()

        self.update_status(f"🏢 已选择单位: {unit}")

    def load_requirements(self):
        """加载当前单位的激光要求"""
        self.req_listbox.delete(0, tk.END)

        if self.current_unit:
            requirements = self.data_manager.get_requirements(self.current_unit)
            for req in requirements:
                self.req_listbox.insert(tk.END, req)

            if requirements:
                self.update_status(f"📋 单位 '{self.current_unit}' 有 {len(requirements)} 个激光要求")
            else:
                self.update_status(f"⚠️ 单位 '{self.current_unit}' 暂无激光要求")
    
    def on_requirement_selected(self, event):
        """激光要求选择事件"""
        selection = self.req_listbox.curselection()
        if selection and self.current_unit:
            requirement = self.req_listbox.get(selection[0])
            self.select_requirement(requirement)
    
    def on_requirement_double_click(self, event):
        """激光要求双击事件"""
        self.on_requirement_selected(event)
        if self.current_requirement:
            self.copy_template()
    
    def select_requirement(self, requirement):
        """选择激光要求"""
        self.current_requirement = requirement

        # 加载模板代码
        template = self.data_manager.get_template(self.current_unit, requirement)

        # 判断是单行还是多行代码
        if '\n' in template and len(template.split('\n')) > 1:
            # 多行代码，显示Text组件
            self.template_entry.pack_forget()
            self.template_text.pack(fill=tk.X, pady=2)
            self.template_text.config(state='normal')
            self.template_text.delete(1.0, tk.END)
            self.template_text.insert(1.0, template)
            self.template_text.config(state='disabled')
        else:
            # 单行代码，显示Entry组件
            self.template_text.pack_forget()
            self.template_entry.pack(fill=tk.X, pady=2)
            self.template_var.set(template)

        self.update_template_info()
        self.update_status(f"✅ 已加载模板: {self.current_unit} - {requirement}")

    def update_template_info(self):
        """更新模板信息显示 - 美化版"""
        if self.current_unit and self.current_requirement:
            info = f"{self.current_unit} - {self.current_requirement}"
            self.template_info_label.config(text=info, foreground=self.colors['text'])
            self.status_indicator.config(text=self.icons['success'])
        elif self.current_unit:
            info = f"{self.current_unit} - 请选择激光要求"
            self.template_info_label.config(text=info, foreground=self.colors['text_secondary'])
            self.status_indicator.config(text=self.icons['warning'])
        else:
            self.template_info_label.config(text="请选择单位和激光要求", foreground=self.colors['text_secondary'])
            self.status_indicator.config(text=self.icons['ready'])
    
    def copy_template(self):
        """复制模板代码到剪贴板"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if template:
            self.root.clipboard_clear()
            self.root.clipboard_append(template)
            self.update_status(f"{self.icons['check']} 模板代码已复制到剪贴板")
            messagebox.showinfo("复制成功", f"{self.icons['check']} 模板代码已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有可复制的模板代码！")

    def clear_template_display(self):
        """清空模板显示"""
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.current_requirement = None
        self.update_template_info()
        self.update_status("🗑️ 已清空模板显示")
    
    def save_template_to_file(self):
        """保存模板代码到文件"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if not template:
            messagebox.showwarning("警告", "没有可保存的模板代码！")
            return

        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="保存模板代码",
            defaultextension=".gcode",
            filetypes=[("G代码文件", "*.gcode"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(template)
                messagebox.showinfo("保存成功", f"{self.icons['check']} 模板代码已保存到:\n{filename}")
                self.update_status(f"{self.icons['save']} 模板已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存文件时出错: {e}")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.root.attributes('-topmost', self.always_on_top.get())
        status = "开启" if self.always_on_top.get() else "关闭"
        icon = self.icons['pin'] if self.always_on_top.get() else "📌"
        self.update_status(f"{icon} 窗口置顶已{status}")

    def refresh_data(self):
        """刷新数据"""
        self.data_manager.load_data()
        self.load_units()
        self.current_unit = None
        self.current_requirement = None
        self.update_template_info()
        messagebox.showinfo("刷新完成", f"{self.icons['check']} 数据已刷新！")
    
    def open_settings(self):
        """打开设置窗口"""
        SettingsWindow(self.root, self.data_manager, self.refresh_data)
    
    def show_help(self):
        """显示使用说明"""
        help_text = """激光模板查询器使用说明：

1. 选择单位：
   - 使用下拉列表选择单位
   - 或点击快速选择按钮

2. 选择激光要求：
   - 在激光要求列表中点击选择
   - 双击可直接复制模板代码

3. 查看模板代码：
   - 选择要求后自动显示对应的模板代码
   - 可复制代码到剪贴板
   - 可保存代码到文件

4. 数据管理：
   - 点击"数据设置"按钮进入管理界面
   - 可添加、编辑、删除单位和要求
   - 支持数据导入导出

5. 其他功能：
   - 窗口置顶：保持窗口在最前面
   - 刷新数据：重新加载数据文件"""
        
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """激光模板查询器 v1.0

一个用于快速查询激光加工模板代码的桌面应用程序。

功能特点：
• 三级查询：单位 → 激光要求 → 模板代码
• 数据管理：支持添加、编辑、删除数据
• 数据持久化：JSON格式存储
• 导入导出：支持数据备份和恢复
• 窗口置顶：方便与其他软件配合使用

开发语言：Python + tkinter
版本：1.0
"""
        messagebox.showinfo("关于", about_text)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
    
    def on_closing(self):
        """程序关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出激光模板查询器吗？"):
            self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = LaserTemplateQueryApp()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")
        print(f"错误详情: {e}")


if __name__ == "__main__":
    main()
