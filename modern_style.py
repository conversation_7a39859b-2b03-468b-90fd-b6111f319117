# -*- coding: utf-8 -*-
"""
现代化界面样式模块
为激光模板查询器提供统一的现代化界面样式
"""

class ModernStyle:
    """现代化界面样式类"""
    
    @staticmethod
    def get_style():
        """获取现代化样式配置"""
        
        # 现代扁平化配色方案
        colors = {
            # 主色调 - 更加专业的蓝色系
            'primary': '#1976d2',           # 主蓝色
            'primary_light': '#42a5f5',     # 浅蓝色
            'primary_dark': '#0d47a1',      # 深蓝色
            'primary_hover': '#1565c0',     # 悬停蓝色

            # 辅助色调 - 更加协调的灰色系
            'secondary': '#546e7a',         # 中性灰
            'secondary_light': '#78909c',   # 浅灰色
            'accent': '#00acc1',            # 青色强调色
            'accent_light': '#26c6da',      # 浅青色

            # 状态色彩 - 更加专业的状态色
            'success': '#2e7d32',           # 成功绿
            'success_light': '#4caf50',     # 浅成功绿
            'warning': '#f57c00',           # 警告橙
            'warning_light': '#ff9800',     # 浅警告橙
            'danger': '#d32f2f',            # 危险红
            'danger_light': '#f44336',      # 浅危险红

            # 背景色彩 - 更加舒适的背景
            'background': '#f5f5f5',        # 浅灰背景
            'background_dark': '#e0e0e0',   # 稍深背景
            'surface': '#ffffff',           # 纯白表面
            'surface_hover': '#fafafa',     # 悬停表面
            'card': '#ffffff',              # 卡片背景
            'card_hover': '#f5f5f5',        # 卡片悬停

            # 文字色彩 - 更加清晰的对比度
            'text': '#212121',              # 主文字 - 深灰黑
            'text_secondary': '#424242',    # 次要文字
            'text_muted': '#757575',        # 静音文字
            'text_inverse': '#ffffff',      # 反色文字

            # 边框色彩 - 更加精致的边框
            'border': '#e0e0e0',            # 默认边框
            'border_focus': '#1976d2',      # 焦点边框
            'border_hover': '#bdbdbd',      # 悬停边框

            # 阴影色彩 - 更加自然的阴影效果
            'shadow_light': 'rgba(0, 0, 0, 0.05)',   # 极浅阴影
            'shadow_medium': 'rgba(0, 0, 0, 0.1)',   # 中等阴影
            'shadow_dark': 'rgba(0, 0, 0, 0.15)',    # 深阴影
        }

        # 现代扁平化图标系统
        icons = {
            # 主要操作图标
            'refresh': '↻',
            'settings': '⚙',
            'copy': '⧉',
            'save': '💾',
            'clear': '✕',
            'export': '↗',
            'import': '↙',

            # 编辑操作图标
            'add': '+',
            'delete': '✕',
            'edit': '✎',
            'rename': '✎',

            # 导航和状态图标
            'search': '🔍',
            'pin': '📌',
            'folder': '📁',
            'file': '📄',
            'check': '✓',
            'warning': '⚠',
            'error': '✕',
            'info': 'ⓘ',

            # 状态指示图标
            'ready': '●',
            'loading': '○',
            'success': '✓',
            'failed': '✕',

            # 功能图标
            'laser': '⚡',
            'template': '📋',
            'unit': '🏢',
            'requirement': '📝',
            'code': '⟨⟩',

            # 装饰图标
            'star': '★',
            'heart': '♥',
            'diamond': '◆',
            'sparkle': '✦'
        }
        
        # 字体配置
        fonts = {
            'title': ('微软雅黑', 12, 'bold'),
            'subtitle': ('微软雅黑', 11, 'bold'),
            'body': ('微软雅黑', 10),
            'small': ('微软雅黑', 9),
            'tiny': ('微软雅黑', 8),
            'code': ('Consolas', 11),
            'code_small': ('Consolas', 10),
            'button': ('微软雅黑', 9),
            'button_bold': ('微软雅黑', 9, 'bold'),
        }
        
        # 边距和间距配置
        spacing = {
            'xs': 2,
            'sm': 4,
            'md': 8,
            'lg': 12,
            'xl': 16,
            'xxl': 24,
        }
        
        # 圆角配置
        radius = {
            'sm': 2,
            'md': 4,
            'lg': 8,
            'xl': 12,
        }
        
        # 返回样式配置
        return {
            'colors': colors,
            'icons': icons,
            'fonts': fonts,
            'spacing': spacing,
            'radius': radius,
        }