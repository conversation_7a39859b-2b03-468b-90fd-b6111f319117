# -*- coding: utf-8 -*-
"""
现代化UI组件模块
为激光模板查询器提供自定义的现代化UI组件
"""

import tkinter as tk
from tkinter import ttk
from modern_style import ModernStyle

# 获取样式配置
style_config = ModernStyle.get_style()
colors = style_config['colors']
fonts = style_config['fonts']
spacing = style_config['spacing']
radius = style_config['radius']


class ModernButton(ttk.Button):
    """现代化按钮"""
    
    def __init__(self, parent, text="", icon=None, command=None, style_type="default", **kwargs):
        """
        初始化现代化按钮
        
        Args:
            parent: 父容器
            text: 按钮文本
            icon: 按钮图标
            command: 点击命令
            style_type: 按钮样式类型 (default, primary, success, danger)
        """
        # 处理图标
        if icon:
            display_text = f"{icon} {text}" if text else icon
        else:
            display_text = text
            
        # 确定样式
        if style_type == "primary":
            button_style = "Primary.TButton"
        elif style_type == "success":
            button_style = "Success.TButton"
        elif style_type == "danger":
            button_style = "Danger.TButton"
        else:
            button_style = "Modern.TButton"
            
        # 初始化按钮
        super().__init__(parent, text=display_text, command=command, style=button_style, **kwargs)
        
        # 添加悬停效果
        self.bind('<Enter>', lambda e: self.configure(cursor='hand2'))
        self.bind('<Leave>', lambda e: self.configure(cursor=''))


class ModernListbox(tk.Frame):
    """现代化列表框"""
    
    def __init__(self, parent, height=10, **kwargs):
        """
        初始化现代化列表框
        
        Args:
            parent: 父容器
            height: 列表框高度
        """
        super().__init__(
            parent, 
            bg=colors['surface'], 
            relief='flat', 
            bd=1,
            highlightbackground=colors['border'],
            highlightthickness=1
        )
        
        # 创建列表框
        self.listbox = tk.Listbox(
            self,
            font=fonts['body'],
            selectbackground=colors['primary'],
            selectforeground=colors['text_inverse'],
            bg=colors['surface'],
            fg=colors['text'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            activestyle='none',
            selectmode=tk.SINGLE,
            height=height,
            **kwargs
        )
        
        # 创建滚动条
        self.scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.listbox.yview)
        self.listbox.configure(yscrollcommand=self.scrollbar.set)
        
        # 布局
        self.listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 1), pady=1)
        
        # 绑定鼠标滚轮事件，防止传播到父窗口
        self.listbox.bind("<MouseWheel>", self._on_mousewheel)
        self.listbox.bind("<Button-4>", self._on_mousewheel)  # Linux
        self.listbox.bind("<Button-5>", self._on_mousewheel)  # Linux
        
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 阻止事件传播
        if event.delta:
            # Windows
            self.listbox.yview_scroll(int(-1 * (event.delta / 120)), "units")
        else:
            # Linux
            if event.num == 4:
                self.listbox.yview_scroll(-1, "units")
            elif event.num == 5:
                self.listbox.yview_scroll(1, "units")
        return "break"  # 阻止事件继续传播
        
    def bind(self, event, callback):
        """绑定事件"""
        self.listbox.bind(event, callback)
        
    def insert(self, index, *elements):
        """插入元素"""
        self.listbox.insert(index, *elements)
        
    def delete(self, first, last=None):
        """删除元素"""
        self.listbox.delete(first, last)
        
    def get(self, index):
        """获取元素"""
        return self.listbox.get(index)
        
    def curselection(self):
        """获取当前选中项"""
        return self.listbox.curselection()
        
    def select_set(self, first, last=None):
        """设置选中项"""
        self.listbox.select_set(first, last)
        
    def select_clear(self, first, last=None):
        """清除选中项"""
        self.listbox.select_clear(first, last)
        
    def size(self):
        """获取列表大小"""
        return self.listbox.size()
        
    def see(self, index):
        """确保元素可见"""
        self.listbox.see(index)


class ModernCard(tk.Frame):
    """现代化卡片"""
    
    def __init__(self, parent, title=None, **kwargs):
        """
        初始化现代化卡片
        
        Args:
            parent: 父容器
            title: 卡片标题
        """
        super().__init__(
            parent, 
            bg=colors['surface'], 
            relief='flat', 
            bd=0,
            highlightbackground=colors['border'],
            highlightthickness=1,
            **kwargs
        )
        
        # 内部容器
        self.inner_frame = tk.Frame(self, bg=colors['surface'])
        self.inner_frame.pack(fill=tk.BOTH, expand=True, padx=spacing['md'], pady=spacing['md'])
        
        # 标题
        if title:
            title_label = tk.Label(
                self.inner_frame,
                text=title,
                font=fonts['subtitle'],
                bg=colors['surface'],
                fg=colors['primary']
            )
            title_label.pack(anchor='w', pady=(0, spacing['sm']))
            
            # 分隔线
            separator = tk.Frame(self.inner_frame, height=1, bg=colors['border'])
            separator.pack(fill=tk.X, pady=spacing['xs'])
            
        # 内容框架
        self.content_frame = tk.Frame(self.inner_frame, bg=colors['surface'])
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(spacing['sm'], 0))


class ModernCombobox(ttk.Combobox):
    """现代化下拉框"""
    
    def __init__(self, parent, values=None, **kwargs):
        """
        初始化现代化下拉框
        
        Args:
            parent: 父容器
            values: 下拉选项
        """
        super().__init__(
            parent,
            font=fonts['body'],
            style='Modern.TCombobox',
            **kwargs
        )
        
        if values:
            self['values'] = values
            
        # 绑定鼠标滚轮事件，防止传播到父窗口
        self.bind("<MouseWheel>", self._on_mousewheel)
        self.bind("<Button-4>", self._on_mousewheel)  # Linux
        self.bind("<Button-5>", self._on_mousewheel)  # Linux
        
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 阻止事件传播
        if event.delta:
            # Windows - 在下拉框中滚动选项
            current = self.current()
            if event.delta > 0 and current > 0:
                self.current(current - 1)
                self.event_generate('<<ComboboxSelected>>')
            elif event.delta < 0 and current < len(self['values']) - 1:
                self.current(current + 1)
                self.event_generate('<<ComboboxSelected>>')
        else:
            # Linux
            current = self.current()
            if event.num == 4 and current > 0:
                self.current(current - 1)
                self.event_generate('<<ComboboxSelected>>')
            elif event.num == 5 and current < len(self['values']) - 1:
                self.current(current + 1)
                self.event_generate('<<ComboboxSelected>>')
        return "break"  # 阻止事件继续传播


class ModernEntry(tk.Entry):
    """现代化输入框"""
    
    def __init__(self, parent, **kwargs):
        """初始化现代化输入框"""
        super().__init__(
            parent,
            font=fonts['body'],
            bg=colors['surface'],
            fg=colors['text'],
            relief='flat',
            bd=0,
            highlightthickness=1,
            highlightbackground=colors['border'],
            highlightcolor=colors['primary'],
            insertbackground=colors['primary'],
            **kwargs
        )


class ModernText(tk.Text):
    """现代化文本框"""
    
    def __init__(self, parent, **kwargs):
        """初始化现代化文本框"""
        super().__init__(
            parent,
            font=fonts['code'],
            bg=colors['surface'],
            fg=colors['text'],
            relief='flat',
            bd=0,
            highlightthickness=1,
            highlightbackground=colors['border'],
            highlightcolor=colors['primary'],
            insertbackground=colors['primary'],
            selectbackground=colors['primary'],
            selectforeground=colors['text_inverse'],
            padx=spacing['md'],
            pady=spacing['md'],
            **kwargs
        )


class ModernScrollableFrame(tk.Frame):
    """现代化可滚动框架"""
    
    def __init__(self, parent, **kwargs):
        """初始化现代化可滚动框架"""
        super().__init__(parent, **kwargs)
        
        # 创建画布
        self.canvas = tk.Canvas(
            self, 
            bg=colors['background'],
            highlightthickness=0,
            **kwargs
        )
        
        # 创建滚动条
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=colors['background'])
        
        # 配置滚动区域
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        # 创建窗口
        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        # 配置画布
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 绑定画布大小变化
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        
        # 绑定鼠标滚轮事件
        self.canvas.bind_all("<MouseWheel>", self.on_mousewheel)
        
        # 布局
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
    
    def on_canvas_configure(self, event):
        """画布大小变化事件"""
        # 调整内部框架宽度以匹配画布
        self.canvas.itemconfig(self.canvas_window, width=event.width)
    
    def on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")


class ModernTooltip:
    """现代化工具提示"""
    
    def __init__(self, widget, text):
        """
        初始化工具提示
        
        Args:
            widget: 要添加提示的组件
            text: 提示文本
        """
        self.widget = widget
        self.text = text
        self.tooltip = None
        
        # 绑定事件
        self.widget.bind("<Enter>", self.show_tooltip)
        self.widget.bind("<Leave>", self.hide_tooltip)
    
    def show_tooltip(self, event=None):
        """显示工具提示"""
        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25
        
        # 创建工具提示窗口
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{x}+{y}")
        
        # 创建标签
        label = tk.Label(
            self.tooltip,
            text=self.text,
            font=fonts['small'],
            bg=colors['secondary'],
            fg=colors['text_inverse'],
            relief="solid",
            borderwidth=1,
            padx=spacing['sm'],
            pady=spacing['xs']
        )
        label.pack()
    
    def hide_tooltip(self, event=None):
        """隐藏工具提示"""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None


def setup_ttk_styles():
    """设置ttk样式"""
    style = ttk.Style()
    
    # 现代扁平化按钮样式
    style.configure('Modern.TButton',
                   padding=(spacing['md'], spacing['sm']),
                   font=fonts['button'],
                   relief='flat',
                   borderwidth=0,
                   focuscolor='none',
                   background=colors['surface'],
                   foreground=colors['text'])

    # 主要按钮样式
    style.configure('Primary.TButton',
                   padding=(spacing['lg'], spacing['md']),
                   font=fonts['button_bold'],
                   relief='flat',
                   borderwidth=0,
                   focuscolor='none',
                   background=colors['primary'],
                   foreground=colors['text_inverse'])

    # 成功按钮样式
    style.configure('Success.TButton',
                   padding=(spacing['md'], spacing['sm']),
                   font=fonts['button_bold'],
                   relief='flat',
                   borderwidth=0,
                   focuscolor='none',
                   background=colors['success'],
                   foreground=colors['text_inverse'])

    # 危险按钮样式
    style.configure('Danger.TButton',
                   padding=(spacing['md'], spacing['sm']),
                   font=fonts['button_bold'],
                   relief='flat',
                   borderwidth=0,
                   focuscolor='none',
                   background=colors['danger'],
                   foreground=colors['text_inverse'])

    # 标签框样式
    style.configure('Modern.TLabelframe',
                   padding=spacing['lg'],
                   relief='flat',
                   borderwidth=0,
                   background=colors['surface'],
                   bordercolor=colors['border'])

    style.configure('Modern.TLabelframe.Label',
                   font=fonts['subtitle'],
                   foreground=colors['primary'],
                   background=colors['surface'],
                   padding=(0, spacing['xs']))

    # 组合框样式
    style.configure('Modern.TCombobox',
                   padding=spacing['md'],
                   font=fonts['body'],
                   relief='flat',
                   borderwidth=0,
                   focuscolor='none',
                   fieldbackground=colors['surface'],
                   bordercolor=colors['border'])

    # 复选框样式
    style.configure('Modern.TCheckbutton',
                   font=fonts['body'],
                   focuscolor='none',
                   background=colors['background'],
                   foreground=colors['text'])
    
    # 滚动条样式
    style.configure('TScrollbar',
                   background=colors['background'],
                   troughcolor=colors['background'],
                   bordercolor=colors['border'],
                   arrowcolor=colors['text_secondary'])
    
    # 设置悬停效果
    style.map('Modern.TButton',
              background=[('active', colors['background_dark']),
                        ('pressed', colors['primary_light'])],
              foreground=[('active', colors['primary']),
                        ('pressed', colors['text_inverse'])],
              bordercolor=[('active', colors['primary_light']),
                         ('pressed', colors['primary'])],
              relief=[('pressed', 'flat'),
                    ('!pressed', 'flat')])

    style.map('Primary.TButton',
              background=[('active', colors['primary_light']),
                        ('pressed', colors['primary_dark'])],
              foreground=[('active', colors['text_inverse']),
                        ('pressed', colors['text_inverse'])],
              relief=[('pressed', 'flat'),
                    ('!pressed', 'flat')])

    style.map('Success.TButton',
              background=[('active', colors['success_light']),
                        ('pressed', colors['success'])],
              foreground=[('active', colors['text_inverse']),
                        ('pressed', colors['text_inverse'])],
              relief=[('pressed', 'flat'),
                    ('!pressed', 'flat')])

    style.map('Danger.TButton',
              background=[('active', colors['danger_light']),
                        ('pressed', colors['danger'])],
              foreground=[('active', colors['text_inverse']),
                        ('pressed', colors['text_inverse'])],
              relief=[('pressed', 'flat'),
                    ('!pressed', 'flat')])

    # 组合框悬停效果
    style.map('Modern.TCombobox',
              fieldbackground=[('readonly', colors['surface']),
                             ('focus', colors['surface_hover'])],
              bordercolor=[('focus', colors['primary']),
                         ('!focus', colors['border'])],
              arrowcolor=[('active', colors['primary']),
                        ('!active', colors['secondary'])])

    # 复选框悬停效果
    style.map('Modern.TCheckbutton',
              background=[('active', colors['background']),
                        ('!active', colors['background'])],
              foreground=[('active', colors['primary']),
                        ('!active', colors['text'])],
              indicatorcolor=[('selected', colors['primary']),
                            ('!selected', colors['surface'])])
    
    return style