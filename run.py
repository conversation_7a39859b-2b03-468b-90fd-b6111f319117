#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激光模板查询器启动脚本
双击此文件即可运行程序
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        messagebox.showerror(
            "版本错误", 
            f"此程序需要Python 3.6或更高版本\n当前版本: {sys.version}\n请升级Python后重试。"
        )
        return False
    return True

def check_dependencies():
    """检查依赖模块"""
    try:
        import tkinter
        import json
        import shutil
        import datetime
        return True
    except ImportError as e:
        messagebox.showerror(
            "依赖错误", 
            f"缺少必要的模块: {e}\n请确保Python环境完整。"
        )
        return False

def main():
    """主启动函数"""
    # 设置工作目录为脚本所在目录
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包的exe中
        script_dir = sys._MEIPASS
    else:
        # 运行在正常Python环境中
        script_dir = os.path.dirname(os.path.abspath(__file__))

    os.chdir(script_dir)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查主程序文件是否存在（仅在非打包环境中检查）
    if not getattr(sys, 'frozen', False) and not os.path.exists('main.py'):
        messagebox.showerror(
            "文件错误",
            "找不到主程序文件 main.py\n请确保所有程序文件都在同一目录下。"
        )
        return
    
    try:
        # 导入并运行主程序
        from main import LaserTemplateQueryApp
        print("激光模板查询器启动中...")
        app = LaserTemplateQueryApp()
        app.run()
    except Exception as e:
        error_msg = f"程序启动失败:\n{str(e)}\n\n请检查程序文件是否完整。"
        print(f"错误: {e}")
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("启动错误", error_msg)
            root.destroy()
        except:
            print(error_msg)

if __name__ == "__main__":
    main()
