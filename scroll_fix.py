# -*- coding: utf-8 -*-
"""
滚轮事件修复模块
解决下拉框和列表框滚轮事件传播到父窗口的问题
"""

import tkinter as tk
from tkinter import ttk

class ScrollEventFixer:
    """滚轮事件修复器"""
    
    def __init__(self, root):
        """
        初始化滚轮事件修复器
        
        Args:
            root: 根窗口
        """
        self.root = root
        self.protected_widgets = []
        
        # 绑定全局滚轮事件
        self.root.bind_all("<MouseWheel>", self._on_global_mousewheel, add='+')
        self.root.bind_all("<Button-4>", self._on_global_mousewheel, add='+')  # Linux
        self.root.bind_all("<Button-5>", self._on_global_mousewheel, add='+')  # Linux
    
    def protect_widget(self, widget):
        """
        保护组件不受滚轮事件传播影响
        
        Args:
            widget: 要保护的组件
        """
        if widget not in self.protected_widgets:
            self.protected_widgets.append(widget)
            
            # 为组件绑定专用的滚轮事件处理
            if isinstance(widget, ttk.Combobox):
                widget.bind("<MouseWheel>", lambda e: self._handle_combobox_scroll(widget, e))
                widget.bind("<Button-4>", lambda e: self._handle_combobox_scroll(widget, e))
                widget.bind("<Button-5>", lambda e: self._handle_combobox_scroll(widget, e))
            elif isinstance(widget, tk.Listbox):
                widget.bind("<MouseWheel>", lambda e: self._handle_listbox_scroll(widget, e))
                widget.bind("<Button-4>", lambda e: self._handle_listbox_scroll(widget, e))
                widget.bind("<Button-5>", lambda e: self._handle_listbox_scroll(widget, e))
    
    def _on_global_mousewheel(self, event):
        """全局滚轮事件处理"""
        try:
            # 获取鼠标位置
            x, y = self.root.winfo_pointerxy()
            
            # 检查是否在受保护的组件上
            for widget in self.protected_widgets:
                if self._is_mouse_over_widget(widget, x, y):
                    # 在受保护的组件上，阻止事件传播
                    return "break"
                    
        except Exception:
            pass
        
        # 不在受保护组件上，允许正常传播
        return None
    
    def _is_mouse_over_widget(self, widget, mouse_x, mouse_y):
        """检查鼠标是否在组件上"""
        try:
            widget_x = widget.winfo_rootx()
            widget_y = widget.winfo_rooty()
            widget_width = widget.winfo_width()
            widget_height = widget.winfo_height()
            
            return (widget_x <= mouse_x <= widget_x + widget_width and 
                   widget_y <= mouse_y <= widget_y + widget_height)
        except Exception:
            return False
    
    def _handle_combobox_scroll(self, combobox, event):
        """处理下拉框滚轮事件"""
        try:
            current = combobox.current()
            values = combobox['values']
            
            if not values:
                return "break"
            
            if event.delta:
                # Windows
                if event.delta > 0 and current > 0:
                    combobox.current(current - 1)
                    combobox.event_generate('<<ComboboxSelected>>')
                elif event.delta < 0 and current < len(values) - 1:
                    combobox.current(current + 1)
                    combobox.event_generate('<<ComboboxSelected>>')
            else:
                # Linux
                if event.num == 4 and current > 0:
                    combobox.current(current - 1)
                    combobox.event_generate('<<ComboboxSelected>>')
                elif event.num == 5 and current < len(values) - 1:
                    combobox.current(current + 1)
                    combobox.event_generate('<<ComboboxSelected>>')
                    
        except Exception:
            pass
        
        return "break"  # 阻止事件传播
    
    def _handle_listbox_scroll(self, listbox, event):
        """处理列表框滚轮事件"""
        try:
            if event.delta:
                # Windows
                listbox.yview_scroll(int(-1 * (event.delta / 120)), "units")
            else:
                # Linux
                if event.num == 4:
                    listbox.yview_scroll(-1, "units")
                elif event.num == 5:
                    listbox.yview_scroll(1, "units")
        except Exception:
            pass
        
        return "break"  # 阻止事件传播

def apply_scroll_fix(root):
    """
    应用滚轮修复到根窗口
    
    Args:
        root: 根窗口
        
    Returns:
        ScrollEventFixer: 修复器实例
    """
    return ScrollEventFixer(root)