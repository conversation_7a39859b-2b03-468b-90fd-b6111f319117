# -*- coding: utf-8 -*-
"""
专门测试下拉框滚轮事件
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modern_style import ModernStyle
from modern_widgets import ModernCombobox, setup_ttk_styles

def create_test_window():
    """创建测试窗口"""
    # 获取样式配置
    style_config = ModernStyle.get_style()
    colors = style_config['colors']
    fonts = style_config['fonts']
    
    # 创建主窗口
    root = tk.Tk()
    root.title("下拉框滚轮事件测试")
    root.geometry("500x400")
    root.configure(bg=colors['background'])
    
    # 设置样式
    setup_ttk_styles()
    
    # 创建可滚动的主框架
    canvas = tk.Canvas(root, bg=colors['background'], highlightthickness=0)
    scrollbar = ttk.Scrollbar(root, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg=colors['background'])
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 绑定画布滚轮事件
    def on_canvas_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        print(f"🖱️ 页面滚动: delta={event.delta}")
    
    canvas.bind_all("<MouseWheel>", on_canvas_mousewheel)
    
    # 布局
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 标题
    title_label = tk.Label(
        scrollable_frame,
        text="🔧 下拉框滚轮事件测试",
        font=fonts['title'],
        bg=colors['background'],
        fg=colors['primary']
    )
    title_label.pack(pady=20)
    
    # 说明文字
    info_label = tk.Label(
        scrollable_frame,
        text="测试说明：在下拉框上滚动鼠标滚轮\n如果只有下拉框选项改变，页面不滚动，说明修复成功\n如果页面跟着滚动，说明还有问题",
        font=fonts['body'],
        bg=colors['background'],
        fg=colors['text'],
        justify=tk.CENTER
    )
    info_label.pack(pady=(0, 20))
    
    # 创建测试下拉框
    test_frame = tk.Frame(scrollable_frame, bg=colors['background'])
    test_frame.pack(pady=20)
    
    tk.Label(
        test_frame,
        text="测试下拉框：",
        font=fonts['body'],
        bg=colors['background'],
        fg=colors['text']
    ).pack(side=tk.LEFT, padx=(0, 10))
    
    # 创建测试用的下拉框
    test_values = [f"单位选项 {i+1}" for i in range(20)]
    test_combobox = ModernCombobox(test_frame, values=test_values, width=20)
    test_combobox.pack(side=tk.LEFT)
    test_combobox.current(0)
    
    # 绑定下拉框选择事件
    def on_combobox_selected(event):
        print(f"📋 下拉框选择: {test_combobox.get()}")
    
    test_combobox.bind('<<ComboboxSelected>>', on_combobox_selected)
    
    # 状态显示
    status_label = tk.Label(
        scrollable_frame,
        text="🎯 请在上方下拉框上滚动鼠标滚轮进行测试",
        font=fonts['body'],
        bg=colors['background'],
        fg=colors['primary']
    )
    status_label.pack(pady=20)
    
    # 添加一些填充内容使页面可滚动
    for i in range(15):
        filler_label = tk.Label(
            scrollable_frame,
            text=f"填充内容 {i+1} - 用于测试页面滚动功能",
            font=fonts['small'],
            bg=colors['background'],
            fg=colors['text_muted']
        )
        filler_label.pack(pady=2)
    
    return root

if __name__ == "__main__":
    print("🔧 启动下拉框滚轮事件测试...")
    print("📋 测试步骤：")
    print("1. 在下拉框上滚动鼠标滚轮")
    print("2. 观察控制台输出和页面行为")
    print("3. 如果只显示'下拉框选择'而没有'页面滚动'，说明修复成功")
    print("-" * 50)
    
    test_root = create_test_window()
    test_root.mainloop()
