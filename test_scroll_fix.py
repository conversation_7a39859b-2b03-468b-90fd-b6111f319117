# -*- coding: utf-8 -*-
"""
测试滚轮事件修复
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modern_style import ModernStyle
from modern_widgets import ModernButton, ModernCard, ModernListbox, ModernCombobox, setup_ttk_styles

def create_test_window():
    """创建测试窗口"""
    # 获取样式配置
    style_config = ModernStyle.get_style()
    colors = style_config['colors']
    fonts = style_config['fonts']
    spacing = style_config['spacing']
    
    # 创建主窗口
    root = tk.Tk()
    root.title("滚轮事件修复测试")
    root.geometry("600x500")
    root.configure(bg=colors['background'])
    
    # 设置样式
    setup_ttk_styles()
    
    # 创建主框架
    main_frame = tk.Frame(root, bg=colors['background'])
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 标题
    title_label = tk.Label(
        main_frame,
        text="🔧 滚轮事件修复测试",
        font=fonts['title'],
        bg=colors['background'],
        fg=colors['primary']
    )
    title_label.pack(pady=(0, 20))
    
    # 说明文字
    info_label = tk.Label(
        main_frame,
        text="测试说明：在下拉框和列表框上滚动鼠标滚轮，\n页面不应该跟着滚动，只有组件内容滚动。",
        font=fonts['body'],
        bg=colors['background'],
        fg=colors['text'],
        justify=tk.CENTER
    )
    info_label.pack(pady=(0, 20))
    
    # 创建测试区域
    test_frame = tk.Frame(main_frame, bg=colors['background'])
    test_frame.pack(fill=tk.BOTH, expand=True)
    
    # 左侧：下拉框测试
    left_card = ModernCard(test_frame, title="📋 下拉框测试")
    left_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
    
    tk.Label(
        left_card.content_frame,
        text="在下拉框上滚动鼠标滚轮：",
        font=fonts['body'],
        bg=colors['surface'],
        fg=colors['text']
    ).pack(pady=10)
    
    # 创建测试用的下拉框
    test_values = [f"选项 {i+1}" for i in range(20)]
    test_combobox = ModernCombobox(left_card.content_frame, values=test_values)
    test_combobox.pack(pady=10, padx=20, fill=tk.X)
    test_combobox.current(0)
    
    tk.Label(
        left_card.content_frame,
        text="✓ 修复后：只有下拉框选项改变\n✗ 修复前：整个页面会滚动",
        font=fonts['small'],
        bg=colors['surface'],
        fg=colors['text_muted'],
        justify=tk.LEFT
    ).pack(pady=10)
    
    # 右侧：列表框测试
    right_card = ModernCard(test_frame, title="📝 列表框测试")
    right_card.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
    
    tk.Label(
        right_card.content_frame,
        text="在列表框上滚动鼠标滚轮：",
        font=fonts['body'],
        bg=colors['surface'],
        fg=colors['text']
    ).pack(pady=10)
    
    # 创建测试用的列表框
    test_listbox = ModernListbox(right_card.content_frame, height=8)
    test_listbox.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
    
    # 添加测试数据
    for i in range(30):
        test_listbox.insert(tk.END, f"列表项 {i+1}")
    
    tk.Label(
        right_card.content_frame,
        text="✓ 修复后：只有列表框内容滚动\n✗ 修复前：整个页面会滚动",
        font=fonts['small'],
        bg=colors['surface'],
        fg=colors['text_muted'],
        justify=tk.LEFT
    ).pack(pady=10)
    
    # 底部状态
    status_frame = tk.Frame(main_frame, bg=colors['background'])
    status_frame.pack(fill=tk.X, pady=(20, 0))
    
    status_label = tk.Label(
        status_frame,
        text="🎉 滚轮事件修复已应用！现在可以正常使用滚轮操作组件了。",
        font=fonts['body'],
        bg=colors['background'],
        fg=colors['success']
    )
    status_label.pack()
    
    # 添加一些额外内容使页面可滚动（用于测试）
    for i in range(5):
        filler_label = tk.Label(
            main_frame,
            text=f"填充内容 {i+1} - 用于测试页面滚动",
            font=fonts['small'],
            bg=colors['background'],
            fg=colors['text_muted']
        )
        filler_label.pack(pady=2)
    
    return root

if __name__ == "__main__":
    test_root = create_test_window()
    print("🔧 滚轮事件修复测试窗口已启动")
    print("📋 测试步骤：")
    print("1. 在下拉框上滚动鼠标滚轮，观察是否只有下拉框选项改变")
    print("2. 在列表框上滚动鼠标滚轮，观察是否只有列表框内容滚动")
    print("3. 在空白区域滚动鼠标滚轮，观察页面是否正常滚动")
    test_root.mainloop()